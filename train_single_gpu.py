#!/usr/bin/env python3
"""
单GPU训练脚本 - 专门为增强版vHeat设计
避免分布式训练的复杂性，专注于单GPU训练
"""

import os
import sys
import argparse
import time
import json
import logging
from pathlib import Path

# 添加classification目录到路径
sys.path.insert(0, 'classification')

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, RandomSampler, SequentialSampler
import torchvision.transforms as transforms
import torchvision.datasets as datasets

# 导入vHeat相关模块
from models import build_model
from utils.config import get_config
from utils.lr_scheduler import build_scheduler
from utils.optimizer import build_optimizer

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(output_dir, 'train.log')),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def build_dataset(data_path, is_train=True, img_size=224):
    """构建数据集"""
    if is_train:
        transform = transforms.Compose([
            transforms.RandomResizedCrop(img_size),
            transforms.RandomHorizontalFlip(),
            transforms.ColorJitter(0.4, 0.4, 0.4, 0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        data_dir = os.path.join(data_path, 'train')
    else:
        transform = transforms.Compose([
            transforms.Resize(int(img_size * 1.143)),  # 256 for 224
            transforms.CenterCrop(img_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        data_dir = os.path.join(data_path, 'val')
    
    dataset = datasets.ImageFolder(data_dir, transform=transform)
    return dataset

def train_one_epoch(model, dataloader, criterion, optimizer, device, logger, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0.0
    correct = 0
    total = 0
    
    for batch_idx, (data, target) in enumerate(dataloader):
        data, target = data.to(device), target.to(device)
        
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        pred = output.argmax(dim=1, keepdim=True)
        correct += pred.eq(target.view_as(pred)).sum().item()
        total += target.size(0)
        
        if batch_idx % 50 == 0:
            logger.info(f'Epoch {epoch}, Batch {batch_idx}/{len(dataloader)}, '
                       f'Loss: {loss.item():.6f}, Acc: {100.*correct/total:.2f}%')
    
    avg_loss = total_loss / len(dataloader)
    accuracy = 100. * correct / total
    
    logger.info(f'Epoch {epoch} Training - Loss: {avg_loss:.6f}, Accuracy: {accuracy:.2f}%')
    return avg_loss, accuracy

def validate(model, dataloader, criterion, device, logger, epoch):
    """验证"""
    model.eval()
    total_loss = 0.0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for data, target in dataloader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            
            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)
    
    avg_loss = total_loss / len(dataloader)
    accuracy = 100. * correct / total
    
    logger.info(f'Epoch {epoch} Validation - Loss: {avg_loss:.6f}, Accuracy: {accuracy:.2f}%')
    return avg_loss, accuracy

def main():
    parser = argparse.ArgumentParser(description='Single GPU Training for Enhanced vHeat')
    
    # 基本参数
    parser.add_argument('--data-path', type=str, required=True, help='Path to dataset')
    parser.add_argument('--output', type=str, default='./output', help='Output directory')
    parser.add_argument('--config', type=str, required=True, help='Config file path')
    
    # 训练参数
    parser.add_argument('--batch-size', type=int, default=256, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=5e-4, help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=0.08, help='Weight decay')
    
    # 模型参数
    parser.add_argument('--num-classes', type=int, default=100, help='Number of classes')
    parser.add_argument('--img-size', type=int, default=224, help='Image size')
    
    # 其他参数
    parser.add_argument('--resume', type=str, default='', help='Resume from checkpoint')
    parser.add_argument('--save-freq', type=int, default=10, help='Save frequency')
    parser.add_argument('--eval-only', action='store_true', help='Only evaluate')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 设置日志
    logger = setup_logging(args.output)
    logger.info(f"Arguments: {json.dumps(vars(args), indent=2)}")
    
    # 模拟配置对象
    class Config:
        def __init__(self):
            # 从配置文件加载
            if args.config.endswith('.yaml'):
                # 简化的配置加载
                import yaml
                with open(args.config, 'r') as f:
                    config_dict = yaml.safe_load(f)
                
                # 设置基本配置
                self.MODEL = type('MODEL', (), {})()
                self.MODEL.TYPE = config_dict['MODEL']['TYPE']
                self.MODEL.NUM_CLASSES = args.num_classes
                
                self.MODEL.VHEAT = type('VHEAT', (), {})()
                vheat_config = config_dict['MODEL']['VHEAT']
                self.MODEL.VHEAT.PATCH_SIZE = vheat_config['PATCH_SIZE']
                self.MODEL.VHEAT.IN_CHANS = vheat_config['IN_CHANS']
                self.MODEL.VHEAT.EMBED_DIM = vheat_config['EMBED_DIM']
                self.MODEL.VHEAT.DEPTHS = vheat_config['DEPTHS']
                self.MODEL.VHEAT.MLP_RATIO = vheat_config['MLP_RATIO']
                self.MODEL.VHEAT.POST_NORM = vheat_config.get('POST_NORM', False)
                self.MODEL.VHEAT.ENABLE_THERMAL_RADIATION = vheat_config.get('ENABLE_THERMAL_RADIATION', True)
                self.MODEL.VHEAT.RADIATION_STRENGTH = vheat_config.get('RADIATION_STRENGTH', 0.1)
                
                self.MODEL.DROP_PATH_RATE = config_dict['MODEL'].get('DROP_PATH_RATE', 0.1)
                
                # 训练配置
                self.TRAIN = type('TRAIN', (), {})()
                train_config = config_dict.get('TRAIN', {})
                self.TRAIN.BASE_LR = train_config.get('BASE_LR', args.lr)
                self.TRAIN.WEIGHT_DECAY = train_config.get('WEIGHT_DECAY', args.weight_decay)
                self.TRAIN.EPOCHS = args.epochs
                
                # 数据配置
                self.DATA = type('DATA', (), {})()
                data_config = config_dict.get('DATA', {})
                self.DATA.IMG_SIZE = data_config.get('IMG_SIZE', args.img_size)
                self.DATA.BATCH_SIZE = args.batch_size
    
    config = Config()
    
    # 构建数据集
    logger.info("Building datasets...")
    train_dataset = build_dataset(args.data_path, is_train=True, img_size=config.DATA.IMG_SIZE)
    val_dataset = build_dataset(args.data_path, is_train=False, img_size=config.DATA.IMG_SIZE)
    
    logger.info(f"Train dataset: {len(train_dataset)} samples")
    logger.info(f"Val dataset: {len(val_dataset)} samples")
    logger.info(f"Number of classes: {len(train_dataset.classes)}")
    
    # 更新类别数
    config.MODEL.NUM_CLASSES = len(train_dataset.classes)
    
    # 构建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.DATA.BATCH_SIZE,
        sampler=RandomSampler(train_dataset),
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.DATA.BATCH_SIZE,
        sampler=SequentialSampler(val_dataset),
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )
    
    # 构建模型
    logger.info(f"Building model: {config.MODEL.TYPE}")
    
    if config.MODEL.TYPE == 'EnhancedvHeat':
        from models.vHeat import EnhancedvHeat
        model = EnhancedvHeat(
            patch_size=config.MODEL.VHEAT.PATCH_SIZE,
            in_chans=config.MODEL.VHEAT.IN_CHANS,
            num_classes=config.MODEL.NUM_CLASSES,
            depths=config.MODEL.VHEAT.DEPTHS,
            dims=config.MODEL.VHEAT.EMBED_DIM,
            drop_path_rate=config.MODEL.DROP_PATH_RATE,
            mlp_ratio=config.MODEL.VHEAT.MLP_RATIO,
            post_norm=config.MODEL.VHEAT.POST_NORM,
            img_size=config.DATA.IMG_SIZE,
            enable_thermal_radiation=config.MODEL.VHEAT.ENABLE_THERMAL_RADIATION,
            radiation_strength=config.MODEL.VHEAT.RADIATION_STRENGTH,
        )
    else:
        from models.vHeat import vHeat
        model = vHeat(
            patch_size=config.MODEL.VHEAT.PATCH_SIZE,
            in_chans=config.MODEL.VHEAT.IN_CHANS,
            num_classes=config.MODEL.NUM_CLASSES,
            depths=config.MODEL.VHEAT.DEPTHS,
            dims=config.MODEL.VHEAT.EMBED_DIM,
            drop_path_rate=config.MODEL.DROP_PATH_RATE,
            mlp_ratio=config.MODEL.VHEAT.MLP_RATIO,
            post_norm=config.MODEL.VHEAT.POST_NORM,
            img_size=config.DATA.IMG_SIZE,
        )
    
    model = model.to(device)
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"Total parameters: {total_params:,}")
    logger.info(f"Trainable parameters: {trainable_params:,}")
    
    # 构建优化器和损失函数
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config.TRAIN.BASE_LR,
        weight_decay=config.TRAIN.WEIGHT_DECAY
    )
    
    criterion = nn.CrossEntropyLoss()
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=config.TRAIN.EPOCHS
    )
    
    # 恢复检查点
    start_epoch = 0
    if args.resume and os.path.exists(args.resume):
        logger.info(f"Resuming from {args.resume}")
        checkpoint = torch.load(args.resume, map_location=device)
        model.load_state_dict(checkpoint['model'])
        optimizer.load_state_dict(checkpoint['optimizer'])
        scheduler.load_state_dict(checkpoint['scheduler'])
        start_epoch = checkpoint['epoch'] + 1
        logger.info(f"Resumed from epoch {start_epoch}")
    
    # 仅评估模式
    if args.eval_only:
        logger.info("Evaluation only mode")
        val_loss, val_acc = validate(model, val_loader, criterion, device, logger, 0)
        return
    
    # 训练循环
    logger.info("Starting training...")
    best_acc = 0.0
    
    for epoch in range(start_epoch, config.TRAIN.EPOCHS):
        logger.info(f"Epoch {epoch}/{config.TRAIN.EPOCHS}")
        
        # 训练
        train_loss, train_acc = train_one_epoch(
            model, train_loader, criterion, optimizer, device, logger, epoch
        )
        
        # 验证
        val_loss, val_acc = validate(
            model, val_loader, criterion, device, logger, epoch
        )
        
        # 更新学习率
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        logger.info(f"Learning rate: {current_lr:.6f}")
        
        # 保存检查点
        is_best = val_acc > best_acc
        if is_best:
            best_acc = val_acc
        
        if epoch % args.save_freq == 0 or is_best:
            checkpoint = {
                'epoch': epoch,
                'model': model.state_dict(),
                'optimizer': optimizer.state_dict(),
                'scheduler': scheduler.state_dict(),
                'best_acc': best_acc,
                'config': vars(args)
            }
            
            checkpoint_path = os.path.join(args.output, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, checkpoint_path)
            logger.info(f"Saved checkpoint: {checkpoint_path}")
            
            if is_best:
                best_path = os.path.join(args.output, 'best_model.pth')
                torch.save(checkpoint, best_path)
                logger.info(f"New best model saved: {best_path} (acc: {best_acc:.2f}%)")
    
    logger.info(f"Training completed! Best accuracy: {best_acc:.2f}%")

if __name__ == "__main__":
    main()
