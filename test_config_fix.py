#!/usr/bin/env python3
"""
测试配置修复是否成功
"""

import sys
import os
sys.path.append('classification')

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    
    try:
        from utils.config import get_config
        
        # 测试增强版配置
        config_path = 'classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml'
        
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        # 模拟命令行参数
        class Args:
            def __init__(self):
                self.cfg = config_path
                self.opts = ['MODEL.NUM_CLASSES', '100']
                self.batch_size = 256
                self.data_path = '/dummy/path'
                self.local_rank = 0
                self.zip = False
                self.cache_mode = 'part'
                self.pretrained = None
                self.resume = None
                self.accumulation_steps = None
                self.use_checkpoint = False
                self.disable_amp = False
                self.output = './output'
                self.tag = None
                self.eval = False
                self.throughput = False
                self.fused_window_process = False
                self.fused_layernorm = False
                self.model_ema = True
                self.model_ema_decay = 0.9999
                self.model_ema_force_cpu = False
        
        args = Args()
        config = get_config(args)
        
        print(f"✅ 配置加载成功!")
        print(f"模型类型: {config.MODEL.TYPE}")
        print(f"模型名称: {config.MODEL.NAME}")
        print(f"类别数: {config.MODEL.NUM_CLASSES}")
        print(f"热辐射启用: {config.MODEL.VHEAT.ENABLE_THERMAL_RADIATION}")
        print(f"辐射强度: {config.MODEL.VHEAT.RADIATION_STRENGTH}")
        print(f"嵌入维度: {config.MODEL.VHEAT.EMBED_DIM}")
        print(f"深度: {config.MODEL.VHEAT.DEPTHS}")
        
        return True, config
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_model_building(config):
    """测试模型构建"""
    print("\n=== 测试模型构建 ===")
    
    try:
        from models import build_model
        
        model = build_model(config)
        print(f"✅ 模型构建成功: {type(model).__name__}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"总参数数量: {total_params:,}")
        
        return True, model
        
    except Exception as e:
        print(f"❌ 模型构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_forward_pass(model):
    """测试前向传播"""
    print("\n=== 测试前向传播 ===")
    
    try:
        import torch
        
        # 创建测试输入
        input_tensor = torch.randn(1, 3, 224, 224)
        
        model.eval()
        with torch.no_grad():
            output = model(input_tensor)
        
        print(f"✅ 前向传播成功!")
        print(f"输入形状: {input_tensor.shape}")
        print(f"输出形状: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("配置修复验证测试")
    print("="*40)
    
    # 1. 测试配置加载
    success, config = test_config_loading()
    if not success:
        print("\n❌ 配置加载失败，请检查配置文件")
        return False
    
    # 2. 测试模型构建
    success, model = test_model_building(config)
    if not success:
        print("\n❌ 模型构建失败")
        return False
    
    # 3. 测试前向传播
    success = test_forward_pass(model)
    if not success:
        print("\n❌ 前向传播失败")
        return False
    
    print("\n" + "="*40)
    print("🎉 所有测试通过！配置修复成功！")
    print("\n现在可以使用以下命令进行训练:")
    print("python train_imagenet100.py --data-path /path/to/imagenet100 --model-type enhanced --model-size tiny --single-gpu --output ./output")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
