#!/usr/bin/env python3
"""
测试EnhancedHeatBlock修复
"""

import sys
import os
sys.path.append('classification')

import torch

def test_enhanced_heat_block():
    """测试EnhancedHeatBlock"""
    print("=== 测试EnhancedHeatBlock ===")
    
    try:
        from models.vHeat import EnhancedHeatBlock, LayerNorm2d
        
        # 创建EnhancedHeatBlock
        block = EnhancedHeatBlock(
            res=14,
            hidden_dim=96,
            drop_path=0.1,
            norm_layer=LayerNorm2d,
            enable_thermal_radiation=True,
            radiation_strength=0.1
        )
        
        print(f"✅ EnhancedHeatBlock创建成功")
        print(f"是否有op属性: {hasattr(block, 'op')}")
        print(f"是否有spatial_heat_conduction属性: {hasattr(block, 'spatial_heat_conduction')}")
        print(f"是否有channel_thermal_radiation属性: {hasattr(block, 'channel_thermal_radiation')}")
        
        # 测试前向传播
        batch_size = 2
        channels = 96
        height = width = 56
        
        x = torch.randn(batch_size, channels, height, width)
        freq_embed = torch.randn(height, width, channels)
        
        print(f"输入形状: {x.shape}")
        print(f"频率嵌入形状: {freq_embed.shape}")
        
        # 前向传播
        block.eval()
        with torch.no_grad():
            output = block(x, freq_embed)
        
        print(f"输出形状: {output.shape}")
        print(f"✅ EnhancedHeatBlock前向传播成功")
        
        return True
        
    except Exception as e:
        print(f"❌ EnhancedHeatBlock测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_vheat_model():
    """测试完整的EnhancedvHeat模型"""
    print("\n=== 测试EnhancedvHeat模型 ===")
    
    try:
        from models.vHeat import EnhancedvHeat
        
        # 创建模型
        model = EnhancedvHeat(
            patch_size=4,
            in_chans=3,
            num_classes=100,
            depths=[2, 2, 6, 2],
            dims=[96, 192, 384, 768],
            enable_thermal_radiation=True,
            radiation_strength=0.1,
            img_size=224
        )
        
        print(f"✅ EnhancedvHeat模型创建成功")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"总参数数量: {total_params:,}")
        
        # 测试前向传播
        batch_size = 1
        x = torch.randn(batch_size, 3, 224, 224)
        
        print(f"输入形状: {x.shape}")
        
        model.eval()
        with torch.no_grad():
            output = model(x)
        
        print(f"输出形状: {output.shape}")
        print(f"✅ EnhancedvHeat模型前向传播成功")
        
        return True
        
    except Exception as e:
        print(f"❌ EnhancedvHeat模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_building():
    """测试通过配置构建模型"""
    print("\n=== 测试通过配置构建模型 ===")
    
    try:
        from utils.config import get_config
        from models import build_model
        
        # 模拟命令行参数
        class Args:
            def __init__(self):
                self.cfg = 'classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml'
                self.opts = ['MODEL.NUM_CLASSES', '100']
                self.batch_size = 256
                self.data_path = '/dummy/path'
                self.local_rank = 0
                self.zip = False
                self.cache_mode = 'part'
                self.pretrained = None
                self.resume = None
                self.accumulation_steps = None
                self.use_checkpoint = False
                self.disable_amp = False
                self.output = './output'
                self.tag = None
                self.eval = False
                self.throughput = False
                self.fused_window_process = False
                self.fused_layernorm = False
                self.model_ema = True
                self.model_ema_decay = 0.9999
                self.model_ema_force_cpu = False
        
        args = Args()
        config = get_config(args)
        
        print(f"配置加载成功: {config.MODEL.TYPE}")
        
        # 构建模型
        model = build_model(config)
        
        print(f"✅ 通过配置构建模型成功: {type(model).__name__}")
        
        # 测试前向传播
        x = torch.randn(1, 3, 224, 224)
        model.eval()
        with torch.no_grad():
            output = model(x)
        
        print(f"输出形状: {output.shape}")
        print(f"✅ 配置构建的模型前向传播成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置构建模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("EnhancedHeatBlock修复验证测试")
    print("="*50)
    
    success_count = 0
    total_tests = 3
    
    # 1. 测试EnhancedHeatBlock
    if test_enhanced_heat_block():
        success_count += 1
    
    # 2. 测试EnhancedvHeat模型
    if test_enhanced_vheat_model():
        success_count += 1
    
    # 3. 测试通过配置构建模型
    if test_model_building():
        success_count += 1
    
    print("\n" + "="*50)
    print(f"测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！修复成功！")
        print("\n现在可以使用以下命令进行训练:")
        print("python train_imagenet100.py --data-path /path/to/imagenet100 --model-type enhanced --model-size tiny --single-gpu --output ./output")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
