MODEL:
  TYPE: EnhancedvHeat
  NAME: EnhancedvHeat_small_imagenet100
  DROP_PATH_RATE: 0.2
  NUM_CLASSES: 100  # ImageNet100有100个类别
  VHEAT:
    EMBED_DIM: 96
    DEPTHS: [ 2, 2, 18, 2 ]
    POST_NORM: False
    # 增强版vHeat特有参数
    ENABLE_THERMAL_RADIATION: True   # 启用热辐射机制
    RADIATION_STRENGTH: 0.1          # 辐射强度 (small模型使用中等值)
    IN_CHANS: 3
    PATCH_SIZE: 4
    MLP_RATIO: 4.0
    LAYER_SCALE: null

TRAIN:
  BASE_LR: 5.e-4
  WEIGHT_DECAY: 0.08
  EPOCHS: 100  # 可以根据需要调整训练轮数
  WARMUP_EPOCHS: 20
  # 增强版模型可能需要稍微不同的训练策略
  MIN_LR: 5.e-6
  WARMUP_LR: 5.e-7
  LR_SCHEDULER:
    NAME: 'cosine'
  OPTIMIZER:
    NAME: 'adamw'
    EPS: 1.e-8
    BETAS: [0.9, 0.999]
    MOMENTUM: 0.9

DATA:
  IMG_SIZE: 224
  DATASET: 'imagenet'  # 使用imagenet数据集格式
  BATCH_SIZE: 128  # small模型可能需要更小的batch size
  INTERPOLATION: 'bicubic'
  PIN_MEMORY: True
  NUM_WORKERS: 8

AUG:
  COLOR_JITTER: 0.4
  AUTO_AUGMENT: 'rand-m9-mstd0.5-inc1'
  REPROB: 0.25
  REMODE: 'pixel'
  RECOUNT: 1
  MIXUP: 0.8
  CUTMIX: 1.0
  CUTMIX_MINMAX: null
  MIXUP_PROB: 1.0
  MIXUP_SWITCH_PROB: 0.5
  MIXUP_MODE: 'batch'

SAVE_FREQ: 5
PRINT_FREQ: 10
EVAL_MODE: False
THROUGHPUT_MODE: False
