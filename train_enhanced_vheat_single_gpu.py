#!/usr/bin/env python3
"""
增强版vHeat单GPU训练脚本
专门为单GPU训练优化，避免分布式训练的复杂性
"""

import os
import sys
import argparse
import subprocess

def main():
    parser = argparse.ArgumentParser(description='Enhanced vHeat Single GPU Training')
    
    # 数据相关参数
    parser.add_argument('--data-path', type=str, required=True,
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--batch-size', type=int, default=256,
                        help='Batch size (default: 256)')
    parser.add_argument('--output', type=str, default='./output',
                        help='Output directory')
    
    # 模型相关参数
    parser.add_argument('--model-size', type=str, default='tiny', 
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--model-type', type=str, default='enhanced', 
                        choices=['original', 'enhanced'],
                        help='Model type: original vHeat or enhanced vHeat (default: enhanced)')
    parser.add_argument('--radiation-strength', type=float, default=None,
                        help='Thermal radiation strength (overrides config default)')
    parser.add_argument('--disable-thermal-radiation', action='store_true',
                        help='Disable thermal radiation mechanism')
    
    # 训练相关参数
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of training epochs (default: 100)')
    parser.add_argument('--resume', type=str, default='',
                        help='Resume from checkpoint')
    parser.add_argument('--eval-only', action='store_true',
                        help='Only evaluate the model')
    
    args = parser.parse_args()
    
    # 验证数据路径
    if not os.path.exists(args.data_path):
        print(f"Error: Data path does not exist: {args.data_path}")
        return 1
    
    # 选择配置文件
    if args.model_type == 'enhanced':
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/EnhancedvHeat_small_imagenet100.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/EnhancedvHeat_base_imagenet100.yaml'
    else:
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet100.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/vHeat_small_imagenet100.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/vHeat_base_imagenet100.yaml'
    
    if not os.path.exists(config_file):
        print(f"Error: Config file does not exist: {config_file}")
        return 1
    
    print(f"Using config: {config_file}")
    print(f"Model type: {args.model_type}")
    print(f"Model size: {args.model_size}")
    print(f"Data path: {args.data_path}")
    print(f"Output: {args.output}")
    print(f"Batch size: {args.batch_size}")
    print(f"Epochs: {args.epochs}")
    
    if args.model_type == 'enhanced':
        print(f"Thermal radiation: {'Disabled' if args.disable_thermal_radiation else 'Enabled'}")
        if args.radiation_strength is not None:
            print(f"Radiation strength: {args.radiation_strength}")
    
    # 构建命令
    cmd = [
        'python', 'classification/main.py',
        '--cfg', config_file,
        '--batch-size', str(args.batch_size),
        '--data-path', args.data_path,
        '--output', args.output,
        '--local_rank', '0'
    ]
    
    # 添加自定义配置选项
    opts = [
        'TRAIN.EPOCHS', str(args.epochs),
        'MODEL.NUM_CLASSES', '100'
    ]
    
    # 如果是增强版模型，添加热辐射相关配置
    if args.model_type == 'enhanced':
        if args.disable_thermal_radiation:
            opts.extend(['MODEL.VHEAT.ENABLE_THERMAL_RADIATION', 'False'])
        
        if args.radiation_strength is not None:
            opts.extend(['MODEL.VHEAT.RADIATION_STRENGTH', str(args.radiation_strength)])
    
    cmd.extend(['--opts'] + opts)
    
    if args.resume:
        cmd.extend(['--resume', args.resume])
    
    if args.eval_only:
        cmd.append('--eval')
    
    # 设置环境变量确保单GPU模式
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = '0'  # 只使用第一个GPU
    # 确保不使用分布式训练
    if 'RANK' in env:
        del env['RANK']
    if 'WORLD_SIZE' in env:
        del env['WORLD_SIZE']
    if 'MASTER_ADDR' in env:
        del env['MASTER_ADDR']
    if 'MASTER_PORT' in env:
        del env['MASTER_PORT']
    
    print(f"Running command: {' '.join(cmd)}")
    print("="*80)
    
    # 执行训练
    try:
        result = subprocess.run(cmd, env=env, check=True)
        print("Training completed successfully!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"Training failed with error code {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("Training interrupted by user")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

# 使用示例:
# 
# # 增强版vHeat tiny模型 (推荐开始)
# python train_enhanced_vheat_single_gpu.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size tiny \
#     --batch-size 256 \
#     --output ./output_enhanced_tiny
# 
# # 增强版vHeat small模型
# python train_enhanced_vheat_single_gpu.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size small \
#     --batch-size 128 \
#     --output ./output_enhanced_small
# 
# # 原始vHeat对比
# python train_enhanced_vheat_single_gpu.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type original \
#     --model-size tiny \
#     --batch-size 256 \
#     --output ./output_original_tiny
# 
# # 消融实验：禁用热辐射
# python train_enhanced_vheat_single_gpu.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size tiny \
#     --disable-thermal-radiation \
#     --output ./output_no_radiation
# 
# # 自定义辐射强度
# python train_enhanced_vheat_single_gpu.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size tiny \
#     --radiation-strength 0.2 \
#     --output ./output_strong_radiation
