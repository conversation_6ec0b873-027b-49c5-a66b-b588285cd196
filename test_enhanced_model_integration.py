#!/usr/bin/env python3
"""
测试增强版vHeat模型集成
验证模型构建、前向传播和训练流程
"""

import sys
import os
sys.path.append('classification')

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset

# 导入模型构建函数
from models import build_model
from utils.config import get_config

def test_model_building():
    """测试模型构建"""
    print("=== 测试模型构建 ===")
    
    # 测试增强版vHeat配置
    config_path = 'classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml'
    
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        return False
    
    try:
        # 加载配置
        config = get_config(config_path)
        print(f"配置加载成功: {config.MODEL.TYPE}")
        print(f"模型名称: {config.MODEL.NAME}")
        print(f"类别数: {config.MODEL.NUM_CLASSES}")
        print(f"热辐射启用: {config.MODEL.VHEAT.ENABLE_THERMAL_RADIATION}")
        print(f"辐射强度: {config.MODEL.VHEAT.RADIATION_STRENGTH}")
        
        # 构建模型
        model = build_model(config)
        print(f"模型构建成功: {type(model).__name__}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数: {trainable_params:,}")
        
        return True, model, config
        
    except Exception as e:
        print(f"模型构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_forward_pass(model, config):
    """测试前向传播"""
    print("\n=== 测试前向传播 ===")
    
    try:
        # 创建测试输入
        batch_size = 2
        input_tensor = torch.randn(batch_size, 3, 224, 224)
        print(f"输入形状: {input_tensor.shape}")
        
        # 前向传播
        model.eval()
        with torch.no_grad():
            output = model(input_tensor)
        
        print(f"输出形状: {output.shape}")
        print(f"预期输出形状: [{batch_size}, {config.MODEL.NUM_CLASSES}]")
        
        # 验证输出形状
        expected_shape = (batch_size, config.MODEL.NUM_CLASSES)
        if output.shape == expected_shape:
            print("✅ 前向传播测试通过")
            return True
        else:
            print(f"❌ 输出形状不匹配: 期望 {expected_shape}, 实际 {output.shape}")
            return False
            
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_pass(model, config):
    """测试反向传播"""
    print("\n=== 测试反向传播 ===")
    
    try:
        # 创建测试数据
        batch_size = 2
        input_tensor = torch.randn(batch_size, 3, 224, 224, requires_grad=True)
        target = torch.randint(0, config.MODEL.NUM_CLASSES, (batch_size,))
        
        # 前向传播
        model.train()
        output = model(input_tensor)
        
        # 计算损失
        criterion = nn.CrossEntropyLoss()
        loss = criterion(output, target)
        print(f"损失值: {loss.item():.6f}")
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        has_grad = False
        grad_norms = []
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                has_grad = True
                grad_norm = param.grad.norm().item()
                grad_norms.append(grad_norm)
                if 'thermal_radiation' in name:
                    print(f"热辐射参数 {name} 梯度范数: {grad_norm:.6f}")
        
        if has_grad:
            avg_grad_norm = sum(grad_norms) / len(grad_norms)
            print(f"平均梯度范数: {avg_grad_norm:.6f}")
            print("✅ 反向传播测试通过")
            return True
        else:
            print("❌ 没有计算到梯度")
            return False
            
    except Exception as e:
        print(f"❌ 反向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_loop(model, config):
    """测试简单的训练循环"""
    print("\n=== 测试训练循环 ===")
    
    try:
        # 创建虚拟数据集
        num_samples = 20
        batch_size = 4
        
        X = torch.randn(num_samples, 3, 224, 224)
        y = torch.randint(0, config.MODEL.NUM_CLASSES, (num_samples,))
        
        dataset = TensorDataset(X, y)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 设置优化器
        optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
        criterion = nn.CrossEntropyLoss()
        
        # 训练几个步骤
        model.train()
        num_steps = 3
        
        for step, (data, target) in enumerate(dataloader):
            if step >= num_steps:
                break
                
            optimizer.zero_grad()
            
            # 前向传播
            output = model(data)
            loss = criterion(output, target)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            # 计算准确率
            pred = output.argmax(dim=1)
            acc = (pred == target).float().mean()
            
            print(f"步骤 {step+1}: 损失={loss.item():.4f}, 准确率={acc.item():.4f}")
        
        print("✅ 训练循环测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 训练循环失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_configs():
    """测试不同配置"""
    print("\n=== 测试不同配置 ===")
    
    configs_to_test = [
        'classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml',
        'classification/configs/vHeat/EnhancedvHeat_small_imagenet100.yaml',
        'classification/configs/vHeat/vHeat_tiny_imagenet100.yaml',  # 原始模型对比
    ]
    
    results = []
    
    for config_path in configs_to_test:
        if not os.path.exists(config_path):
            print(f"跳过不存在的配置: {config_path}")
            continue
            
        try:
            config = get_config(config_path)
            model = build_model(config)
            
            # 快速前向传播测试
            test_input = torch.randn(1, 3, 224, 224)
            model.eval()
            with torch.no_grad():
                output = model(test_input)
            
            total_params = sum(p.numel() for p in model.parameters())
            
            results.append({
                'config': os.path.basename(config_path),
                'model_type': config.MODEL.TYPE,
                'params': total_params,
                'success': True
            })
            
            print(f"✅ {os.path.basename(config_path)}: {total_params:,} 参数")
            
        except Exception as e:
            results.append({
                'config': os.path.basename(config_path),
                'success': False,
                'error': str(e)
            })
            print(f"❌ {os.path.basename(config_path)}: {e}")
    
    return results

def main():
    """主测试函数"""
    print("增强版vHeat模型集成测试")
    print("="*50)
    
    # 1. 测试模型构建
    success, model, config = test_model_building()
    if not success:
        print("模型构建失败，终止测试")
        return False
    
    # 2. 测试前向传播
    if not test_forward_pass(model, config):
        print("前向传播测试失败")
        return False
    
    # 3. 测试反向传播
    if not test_backward_pass(model, config):
        print("反向传播测试失败")
        return False
    
    # 4. 测试训练循环
    if not test_training_loop(model, config):
        print("训练循环测试失败")
        return False
    
    # 5. 测试不同配置
    test_different_configs()
    
    print("\n" + "="*50)
    print("🎉 所有测试通过！增强版vHeat模型集成成功！")
    print("\n可以开始使用以下命令进行训练:")
    print("python train_imagenet100.py --data-path /path/to/imagenet100 --model-type enhanced --model-size tiny --single-gpu --output ./output")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
